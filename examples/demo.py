#!/usr/bin/env python3
"""
Real-time Game Text Recognition Demo

This demo application showcases the real-time text recognition capabilities
with visual feedback and performance metrics.
"""

import sys
import time
import logging
from pathlib import Path
import cv2
import numpy as np
from typing import List, Dict

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from ocr_pipeline.core import OCRResult
from ocr_pipeline.realtime_processor import RealtimeOCRProcessor, RealtimeConfig, PerformanceMetrics
from capture.screen_capture import ScreenCapture, CaptureConfig, CaptureRegion
from utils.config_manager import ConfigManager
from utils.text_processing import TextProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OCRDemo:
    """
    Demo application for real-time OCR
    """
    
    def __init__(self):
        """Initialize the demo application"""
        self.config_manager = ConfigManager()
        self.system_config = self.config_manager.get_system_config()
        
        # Initialize components
        self.screen_capture = ScreenCapture(self.system_config.capture)
        self.realtime_processor = RealtimeOCRProcessor(self.system_config.realtime)
        self.text_processor = TextProcessor()
        
        # Demo state
        self.is_running = False
        self.latest_results = []
        self.performance_metrics = PerformanceMetrics()
        
        # Visual feedback
        self.display_window = "Real-time OCR Demo"
        self.overlay_frame = None
        
        # Setup callbacks
        self.realtime_processor.config.result_callback = self._on_ocr_results
        self.realtime_processor.config.performance_callback = self._on_performance_update
        
        logger.info("OCR Demo initialized successfully")
    
    def start_demo(self, demo_mode: str = "full_screen"):
        """
        Start the demo application
        
        Args:
            demo_mode: Demo mode (full_screen, gaming_regions, custom_region)
        """
        try:
            logger.info(f"Starting OCR demo in {demo_mode} mode")
            
            # Setup capture based on mode
            if demo_mode == "gaming_regions":
                self._setup_gaming_regions()
            elif demo_mode == "custom_region":
                self._setup_custom_region()
            
            # Start components
            self.screen_capture.start_continuous_capture()
            self.realtime_processor.start_processing()
            
            self.is_running = True
            
            # Create display window
            cv2.namedWindow(self.display_window, cv2.WINDOW_NORMAL)
            cv2.resizeWindow(self.display_window, 1200, 800)
            
            # Main demo loop
            self._demo_loop()
            
        except KeyboardInterrupt:
            logger.info("Demo interrupted by user")
        except Exception as e:
            logger.error(f"Demo failed: {e}")
        finally:
            self.stop_demo()
    
    def _setup_gaming_regions(self):
        """Setup gaming-specific capture regions"""
        monitor_info = self.screen_capture.get_monitor_info()
        if len(monitor_info) > 1:
            monitor = monitor_info[1]  # Primary monitor
            regions = self.screen_capture.create_gaming_regions(
                monitor['width'], 
                monitor['height']
            )
            
            logger.info(f"Created {len(regions)} gaming regions:")
            for region in regions:
                logger.info(f"  {region}")
    
    def _setup_custom_region(self):
        """Setup custom capture region (center of screen)"""
        monitor_info = self.screen_capture.get_monitor_info()
        if len(monitor_info) > 1:
            monitor = monitor_info[1]
            # Capture center quarter of screen
            width = monitor['width'] // 2
            height = monitor['height'] // 2
            x = monitor['width'] // 4
            y = monitor['height'] // 4
            
            custom_region = CaptureRegion(x, y, width, height, "center_region")
            logger.info(f"Custom region: {custom_region}")
    
    def _demo_loop(self):
        """Main demo loop"""
        logger.info("Demo started. Press 'q' to quit, 'p' to change profile, 's' to save screenshot")
        
        frame_count = 0
        last_fps_time = time.time()
        
        while self.is_running:
            # Capture frame
            frame = self.screen_capture.capture_frame()
            
            if frame is not None:
                # Process frame
                self.realtime_processor.process_frame(frame)
                
                # Create visualization
                display_frame = self._create_visualization(frame)
                
                # Show frame
                cv2.imshow(self.display_window, display_frame)
                
                frame_count += 1
                
                # Calculate FPS
                current_time = time.time()
                if current_time - last_fps_time >= 1.0:
                    fps = frame_count / (current_time - last_fps_time)
                    logger.debug(f"Display FPS: {fps:.1f}")
                    frame_count = 0
                    last_fps_time = current_time
            
            # Handle keyboard input
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('p'):
                self._cycle_performance_profile()
            elif key == ord('s'):
                self._save_screenshot(frame)
            elif key == ord('r'):
                self._reset_metrics()
            elif key == ord('h'):
                self._show_help()
    
    def _create_visualization(self, frame: np.ndarray) -> np.ndarray:
        """
        Create visualization with OCR results and performance metrics
        
        Args:
            frame: Input frame
            
        Returns:
            Visualization frame
        """
        # Resize frame for display
        display_height = 600
        aspect_ratio = frame.shape[1] / frame.shape[0]
        display_width = int(display_height * aspect_ratio)
        display_frame = cv2.resize(frame, (display_width, display_height))
        
        # Draw OCR results
        for result in self.latest_results:
            self._draw_ocr_result(display_frame, result, display_width / frame.shape[1])
        
        # Add performance overlay
        self._draw_performance_overlay(display_frame)
        
        # Add instructions
        self._draw_instructions(display_frame)
        
        return display_frame
    
    def _draw_ocr_result(self, frame: np.ndarray, result: OCRResult, scale_factor: float):
        """Draw OCR result on frame"""
        # Scale bounding box
        scaled_bbox = []
        for point in result.bbox:
            scaled_point = [int(point[0] * scale_factor), int(point[1] * scale_factor)]
            scaled_bbox.append(scaled_point)
        
        # Draw bounding box
        pts = np.array(scaled_bbox, np.int32)
        pts = pts.reshape((-1, 1, 2))
        
        # Color based on confidence
        if result.confidence > 0.8:
            color = (0, 255, 0)  # Green for high confidence
        elif result.confidence > 0.5:
            color = (0, 255, 255)  # Yellow for medium confidence
        else:
            color = (0, 0, 255)  # Red for low confidence
        
        cv2.polylines(frame, [pts], True, color, 2)
        
        # Draw text
        text_pos = (scaled_bbox[0][0], scaled_bbox[0][1] - 10)
        text = f"{result.text} ({result.confidence:.2f})"
        cv2.putText(frame, text, text_pos, cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
    
    def _draw_performance_overlay(self, frame: np.ndarray):
        """Draw performance metrics overlay"""
        overlay_y = 30
        line_height = 25
        
        metrics_text = [
            f"FPS: {self.performance_metrics.current_fps:.1f}",
            f"Avg FPS: {self.performance_metrics.avg_fps:.1f}",
            f"Frames: {self.performance_metrics.frames_processed}",
            f"Skipped: {self.performance_metrics.frames_skipped}",
            f"Memory: {self.performance_metrics.memory_usage_mb:.1f} MB",
            f"CPU: {self.performance_metrics.cpu_usage_percent:.1f}%"
        ]
        
        for i, text in enumerate(metrics_text):
            y_pos = overlay_y + i * line_height
            cv2.putText(frame, text, (10, y_pos), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            cv2.putText(frame, text, (10, y_pos), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    
    def _draw_instructions(self, frame: np.ndarray):
        """Draw instruction text"""
        instructions = [
            "Controls:",
            "Q - Quit",
            "P - Change Profile", 
            "S - Save Screenshot",
            "R - Reset Metrics",
            "H - Show Help"
        ]
        
        start_y = frame.shape[0] - len(instructions) * 20 - 10
        
        for i, instruction in enumerate(instructions):
            y_pos = start_y + i * 20
            cv2.putText(frame, instruction, (10, y_pos), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
            cv2.putText(frame, instruction, (10, y_pos), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)
    
    def _cycle_performance_profile(self):
        """Cycle through available performance profiles"""
        profiles = self.config_manager.get_available_profiles()
        if not profiles:
            logger.warning("No performance profiles available")
            return
        
        current_index = profiles.index(self.config_manager.current_profile)
        next_index = (current_index + 1) % len(profiles)
        next_profile = profiles[next_index]
        
        self.config_manager.set_profile(next_profile)
        self.system_config = self.config_manager.get_system_config()
        self.realtime_processor.update_config(self.system_config.realtime)
        
        logger.info(f"Switched to profile: {next_profile}")
    
    def _save_screenshot(self, frame: np.ndarray):
        """Save current frame as screenshot"""
        if frame is not None:
            timestamp = int(time.time())
            filename = f"screenshot_{timestamp}.png"
            cv2.imwrite(filename, frame)
            logger.info(f"Screenshot saved: {filename}")
    
    def _reset_metrics(self):
        """Reset performance metrics"""
        self.realtime_processor.reset_metrics()
        logger.info("Performance metrics reset")
    
    def _show_help(self):
        """Show help information"""
        help_text = """
        Real-time OCR Demo Help:
        
        This demo captures your screen and performs real-time text recognition.
        Detected text is highlighted with colored bounding boxes:
        - Green: High confidence (>80%)
        - Yellow: Medium confidence (50-80%)
        - Red: Low confidence (<50%)
        
        Performance metrics are shown in the top-left corner.
        Use the keyboard controls to interact with the demo.
        """
        logger.info(help_text)
    
    def _on_ocr_results(self, results: List[OCRResult]):
        """Callback for OCR results"""
        self.latest_results = results
        
        if results:
            logger.debug(f"Received {len(results)} OCR results")
            for result in results[:3]:  # Log first 3 results
                logger.debug(f"  '{result.text}' (confidence: {result.confidence:.2f})")
    
    def _on_performance_update(self, metrics: PerformanceMetrics):
        """Callback for performance metrics"""
        self.performance_metrics = metrics
    
    def stop_demo(self):
        """Stop the demo application"""
        self.is_running = False
        
        # Stop components
        self.realtime_processor.stop_processing()
        self.screen_capture.stop_continuous_capture()
        
        # Close windows
        cv2.destroyAllWindows()
        
        logger.info("Demo stopped")


def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Real-time OCR Demo")
    parser.add_argument(
        "--mode", 
        choices=["full_screen", "gaming_regions", "custom_region"],
        default="full_screen",
        help="Demo mode"
    )
    parser.add_argument(
        "--profile",
        default="balanced",
        help="Performance profile to use"
    )
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level"
    )
    
    args = parser.parse_args()
    
    # Set logging level
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # Create and run demo
    demo = OCRDemo()
    
    # Set initial profile
    if args.profile != "balanced":
        demo.config_manager.set_profile(args.profile)
        demo.system_config = demo.config_manager.get_system_config()
        demo.realtime_processor.update_config(demo.system_config.realtime)
    
    demo.start_demo(args.mode)


if __name__ == "__main__":
    main()
