#!/usr/bin/env python3
"""
Installation Test Script

This script tests the installation and basic functionality of the
real-time OCR pipeline components.
"""

import sys
import logging
from pathlib import Path
import numpy as np
import cv2

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_imports():
    """Test if all required modules can be imported"""
    logger.info("Testing imports...")
    
    try:
        # Test core dependencies
        import paddleocr
        import mss
        import cv2
        import numpy as np
        import yaml
        logger.info("✓ Core dependencies imported successfully")
        
        # Test our modules
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

        from ocr_pipeline.core import OCRPipeline, OCRConfig
        from capture.screen_capture import ScreenCapture, CaptureConfig
        from preprocessing.image_processor import ImageProcessor, PreprocessingConfig
        from utils.config_manager import ConfigManager
        from utils.text_processing import TextProcessor
        logger.info("✓ OCR pipeline modules imported successfully")
        
        return True
        
    except ImportError as e:
        logger.error(f"✗ Import failed: {e}")
        return False


def test_ocr_pipeline():
    """Test OCR pipeline initialization"""
    logger.info("Testing OCR pipeline...")

    try:
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

        from ocr_pipeline.core import OCRPipeline, OCRConfig
        
        # Create test configuration
        config = OCRConfig(use_gpu=False)  # Use CPU for testing
        
        # Initialize pipeline
        pipeline = OCRPipeline(config)
        logger.info("✓ OCR pipeline initialized successfully")
        
        # Test with a simple image
        test_image = create_test_image()
        results = pipeline.recognize_text(test_image)
        
        logger.info(f"✓ OCR processing completed, found {len(results)} text regions")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ OCR pipeline test failed: {e}")
        return False


def test_screen_capture():
    """Test screen capture functionality"""
    logger.info("Testing screen capture...")

    try:
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

        from capture.screen_capture import ScreenCapture, CaptureConfig
        
        # Initialize screen capture
        config = CaptureConfig()
        capture = ScreenCapture(config)
        
        # Test single frame capture
        frame = capture.capture_frame()
        
        if frame is not None:
            logger.info(f"✓ Screen capture successful, frame shape: {frame.shape}")
            return True
        else:
            logger.error("✗ Screen capture returned None")
            return False
            
    except Exception as e:
        logger.error(f"✗ Screen capture test failed: {e}")
        return False


def test_image_preprocessing():
    """Test image preprocessing"""
    logger.info("Testing image preprocessing...")

    try:
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

        from preprocessing.image_processor import ImageProcessor, PreprocessingConfig
        
        # Initialize processor
        config = PreprocessingConfig()
        processor = ImageProcessor(config)
        
        # Test with sample image
        test_image = create_test_image()
        processed_image = processor.process_image(test_image)
        
        logger.info(f"✓ Image preprocessing successful, output shape: {processed_image.shape}")
        return True
        
    except Exception as e:
        logger.error(f"✗ Image preprocessing test failed: {e}")
        return False


def test_config_manager():
    """Test configuration manager"""
    logger.info("Testing configuration manager...")

    try:
        import sys
        import os
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

        from utils.config_manager import ConfigManager
        
        # Initialize config manager
        config_manager = ConfigManager()
        
        # Test getting system config
        system_config = config_manager.get_system_config()
        
        # Test getting available profiles
        profiles = config_manager.get_available_profiles()
        
        logger.info(f"✓ Configuration manager working, {len(profiles)} profiles available")
        return True
        
    except Exception as e:
        logger.error(f"✗ Configuration manager test failed: {e}")
        return False


def create_test_image():
    """Create a simple test image with text"""
    # Create white background
    image = np.ones((200, 400, 3), dtype=np.uint8) * 255
    
    # Add some text
    cv2.putText(image, "Test Text", (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 2, (0, 0, 0), 3)
    cv2.putText(image, "OCR Pipeline", (50, 150), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    
    return image


def run_all_tests():
    """Run all tests"""
    logger.info("=" * 50)
    logger.info("Real-time OCR Pipeline Installation Test")
    logger.info("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Screen Capture Test", test_screen_capture),
        ("Image Preprocessing Test", test_image_preprocessing),
        ("Configuration Manager Test", test_config_manager),
        ("OCR Pipeline Test", test_ocr_pipeline),  # OCR test last as it takes longest
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("Test Summary:")
    logger.info("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Installation is working correctly.")
        logger.info("\nYou can now run the demo with:")
        logger.info("  python examples/demo.py")
    else:
        logger.error("❌ Some tests failed. Please check the error messages above.")
        logger.info("\nTroubleshooting tips:")
        logger.info("1. Make sure all dependencies are installed: pip install -r requirements.txt")
        logger.info("2. Check that PaddleOCR models are downloaded correctly")
        logger.info("3. Verify screen capture permissions on your system")
    
    return passed == total


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
