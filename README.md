# Real-time Game Text Recognition Pipeline

A high-performance, real-time text recognition system designed specifically for extracting and transcribing dynamic in-game text from live video streams or screen captures. Built with PaddleOCR for state-of-the-art accuracy and optimized for gaming applications.

## Features

### 🚀 High Performance
- **Real-time processing** at 10-30 FPS depending on configuration
- **GPU acceleration** support with PaddlePaddle
- **Asynchronous processing** with threading and frame skipping
- **Memory optimization** and performance monitoring

### 🎮 Gaming Optimized
- **Gaming-specific preprocessing** for UI elements and text
- **Predefined regions** for common gaming layouts (MMO, FPS, RTS)
- **Gaming vocabulary** and text correction
- **Temporal filtering** for consistent text detection

### 🔧 Advanced OCR Pipeline
- **PaddleOCR PP-OCRv5** for maximum accuracy
- **PGNet model** option for speed-optimized scenarios
- **Multi-language support** (English, Chinese, Japanese, Korean)
- **Confidence-based filtering** and text validation

### 📊 Comprehensive Processing
- **Image preprocessing** with adaptive thresholding and noise reduction
- **Text post-processing** with spell checking and gaming corrections
- **Region-based capture** with configurable areas of interest
- **Performance profiling** with detailed metrics

## Quick Start

### Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd real-time-game-text-recognition
   ```

2. **Install dependencies:**
   ```bash
   python3 setup.py
   ```
   
   Or manually:
   ```bash
   pip install -r requirements.txt
   ```

3. **Test the installation:**
   ```bash
   python examples/test_installation.py
   ```

### Basic Usage

**Run the demo application:**
```bash
python examples/demo.py
```

**Demo controls:**
- `Q` - Quit the demo
- `P` - Cycle through performance profiles
- `S` - Save screenshot
- `R` - Reset performance metrics
- `H` - Show help

**Command line options:**
```bash
python examples/demo.py --mode gaming_regions --profile speed --log-level DEBUG
```

## Architecture

### Core Components

1. **OCR Pipeline** (`src/ocr_pipeline/`)
   - Core OCR functionality with PaddleOCR integration
   - Real-time processing framework with threading
   - Performance optimization and monitoring

2. **Screen Capture** (`src/capture/`)
   - High-performance screen capture using MSS
   - Multi-region capture support
   - Gaming-specific region presets

3. **Image Preprocessing** (`src/preprocessing/`)
   - Gaming-optimized image enhancement
   - Adaptive thresholding and noise reduction
   - Text isolation and morphological operations

4. **Text Processing** (`src/utils/`)
   - Post-processing and text correction
   - Gaming vocabulary and spell checking
   - Temporal consistency filtering

5. **Configuration System** (`config/`)
   - YAML-based configuration
   - Performance profiles (accuracy, balanced, speed)
   - Gaming-specific presets

### Processing Pipeline

```
Screen Capture → Image Preprocessing → OCR Recognition → Text Post-processing → Results
     ↓                    ↓                   ↓                    ↓
Performance      Gaming Enhancement    PP-OCRv5/PGNet    Gaming Corrections
Monitoring       & Noise Reduction     Text Detection    & Validation
```

## Configuration

### Performance Profiles

**Accuracy Profile:**
- PP-OCRv5 model with high confidence threshold
- Enhanced preprocessing with scaling
- Lower FPS for maximum accuracy

**Balanced Profile (Default):**
- PP-OCRv5 model with standard settings
- Gaming-optimized preprocessing
- 10 FPS target for real-time performance

**Speed Profile:**
- PGNet model for fastest processing
- Basic preprocessing
- 20+ FPS for high-speed scenarios

### Gaming Presets

**MMO Games:**
- Chat area, UI top bar, minimap regions
- Focus on health, mana, level, experience text

**FPS Games:**
- HUD, ammo counter, health indicator regions
- Focus on health, armor, ammo, score text

**RTS Games:**
- Resources, minimap, unit panel regions
- Focus on resources, population, unit text

## Advanced Usage

### Custom Configuration

Edit `config/config.yaml` to customize:
- OCR model parameters
- Screen capture regions
- Preprocessing settings
- Performance thresholds

### Programming Interface

```python
from src.ocr_pipeline.realtime_processor import RealtimeOCRProcessor
from src.utils.config_manager import ConfigManager

# Initialize with custom config
config_manager = ConfigManager()
config_manager.set_profile("speed")
system_config = config_manager.get_system_config()

# Create processor
processor = RealtimeOCRProcessor(system_config.realtime)

# Set up callbacks
def on_results(results):
    for result in results:
        print(f"Detected: '{result.text}' (confidence: {result.confidence:.2f})")

processor.config.result_callback = on_results

# Start processing
processor.start_processing()

# Process frames
processor.process_frame(your_frame)
```

## Performance Optimization

### Hardware Requirements

**Minimum:**
- CPU: 4+ cores
- RAM: 8GB
- GPU: Optional (CPU-only mode available)

**Recommended:**
- CPU: 8+ cores
- RAM: 16GB
- GPU: NVIDIA GPU with 4GB+ VRAM
- Storage: SSD for faster model loading

### Optimization Tips

1. **Use GPU acceleration** when available
2. **Adjust target FPS** based on your needs
3. **Enable frame skipping** for consistent performance
4. **Use region capture** instead of full screen
5. **Choose appropriate performance profile**

## Troubleshooting

### Common Issues

**PaddleOCR installation fails:**
- Try CPU version: `pip install paddlepaddle` instead of `paddlepaddle-gpu`
- Check CUDA compatibility for GPU version

**Screen capture not working:**
- Check screen recording permissions on macOS
- Run as administrator on Windows if needed

**Low performance:**
- Switch to "speed" profile
- Enable frame skipping
- Reduce target FPS
- Use smaller capture regions

**Poor text recognition:**
- Switch to "accuracy" profile
- Adjust confidence threshold
- Check image preprocessing settings
- Ensure good contrast in source material

### Debug Mode

Enable debug logging for detailed information:
```bash
python examples/demo.py --log-level DEBUG
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- **PaddleOCR** team for the excellent OCR framework
- **MSS** library for high-performance screen capture
- **OpenCV** for image processing capabilities
