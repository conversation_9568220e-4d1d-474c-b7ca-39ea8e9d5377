"""
Advanced Post-processing for OCR Results

This module provides comprehensive post-processing capabilities including
confidence-based filtering, text stitching, validation, and gaming-specific
text analysis and correction.
"""

import re
import logging
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass
import numpy as np
from collections import defaultdict, Counter
import difflib

from ..ocr_pipeline.core import OCRResult
from .text_processing import TextProcessor, TextRegion

logger = logging.getLogger(__name__)


@dataclass
class TextCorrection:
    """Represents a text correction"""
    original: str
    corrected: str
    confidence: float
    correction_type: str


class PostProcessor:
    """
    Advanced post-processing for OCR results with gaming optimizations
    """
    
    def __init__(self, 
                 enable_spell_check: bool = True,
                 enable_gaming_corrections: bool = True,
                 enable_temporal_filtering: bool = True,
                 temporal_window_size: int = 5):
        """
        Initialize post-processor
        
        Args:
            enable_spell_check: Enable spell checking and correction
            enable_gaming_corrections: Enable gaming-specific corrections
            enable_temporal_filtering: Enable temporal consistency filtering
            temporal_window_size: Number of frames to consider for temporal filtering
        """
        self.enable_spell_check = enable_spell_check
        self.enable_gaming_corrections = enable_gaming_corrections
        self.enable_temporal_filtering = enable_temporal_filtering
        self.temporal_window_size = temporal_window_size
        
        # Initialize text processor
        self.text_processor = TextProcessor()
        
        # Gaming vocabulary and corrections
        self.gaming_vocabulary = self._load_gaming_vocabulary()
        self.common_corrections = self._load_common_corrections()
        
        # Temporal filtering
        self.text_history = []
        self.stable_text_regions = {}
        
        # Performance tracking
        self.correction_stats = {
            'total_corrections': 0,
            'spell_corrections': 0,
            'gaming_corrections': 0,
            'temporal_corrections': 0
        }
    
    def process_results(self, results: List[OCRResult], frame_timestamp: float = None) -> List[OCRResult]:
        """
        Apply comprehensive post-processing to OCR results
        
        Args:
            results: List of OCR results to process
            frame_timestamp: Timestamp of the frame (for temporal filtering)
            
        Returns:
            Post-processed OCR results
        """
        if not results:
            return results
        
        # Step 1: Basic filtering
        filtered_results = self.text_processor.filter_results(results)
        
        # Step 2: Text correction
        corrected_results = self._apply_text_corrections(filtered_results)
        
        # Step 3: Temporal filtering
        if self.enable_temporal_filtering and frame_timestamp:
            corrected_results = self._apply_temporal_filtering(corrected_results, frame_timestamp)
        
        # Step 4: Merge nearby text regions
        merged_regions = self.text_processor.merge_nearby_text(corrected_results)
        
        # Step 5: Convert back to OCR results
        final_results = self._regions_to_results(merged_regions)
        
        return final_results
    
    def _apply_text_corrections(self, results: List[OCRResult]) -> List[OCRResult]:
        """Apply text corrections to OCR results"""
        corrected_results = []
        
        for result in results:
            corrected_text = result.text
            correction_applied = False
            
            # Gaming-specific corrections
            if self.enable_gaming_corrections:
                gaming_corrected = self._apply_gaming_corrections(corrected_text)
                if gaming_corrected != corrected_text:
                    corrected_text = gaming_corrected
                    correction_applied = True
                    self.correction_stats['gaming_corrections'] += 1
            
            # Spell checking and correction
            if self.enable_spell_check:
                spell_corrected = self._apply_spell_corrections(corrected_text)
                if spell_corrected != corrected_text:
                    corrected_text = spell_corrected
                    correction_applied = True
                    self.correction_stats['spell_corrections'] += 1
            
            # Create corrected result
            if correction_applied:
                self.correction_stats['total_corrections'] += 1
                # Slightly reduce confidence for corrected text
                new_confidence = result.confidence * 0.95
            else:
                new_confidence = result.confidence
            
            corrected_result = OCRResult(
                text=corrected_text,
                confidence=new_confidence,
                bbox=result.bbox,
                timestamp=result.timestamp
            )
            
            corrected_results.append(corrected_result)
        
        return corrected_results
    
    def _apply_gaming_corrections(self, text: str) -> str:
        """Apply gaming-specific text corrections"""
        # Common OCR errors in gaming contexts
        corrections = {
            # Number/letter confusions
            'O': '0',  # Letter O to number 0
            'l': '1',  # Lowercase L to number 1
            'I': '1',  # Uppercase I to number 1
            'S': '5',  # S to 5 in damage numbers
            'B': '8',  # B to 8
            
            # Common gaming terms
            'HP': 'HP',
            'MP': 'MP',
            'XP': 'XP',
            'Lv': 'Lv',
            'Lvl': 'Lvl',
            'Level': 'Level',
            
            # UI elements
            'Heaith': 'Health',
            'Manna': 'Mana',
            'Experiance': 'Experience',
            'Inventary': 'Inventory',
            'Skilis': 'Skills',
            'Achievments': 'Achievements'
        }
        
        # Apply direct corrections
        for wrong, correct in corrections.items():
            text = text.replace(wrong, correct)
        
        # Pattern-based corrections for damage numbers
        # Fix common OCR errors in damage numbers
        damage_pattern = re.compile(r'-?\d+')
        if damage_pattern.match(text.strip()):
            # Clean up damage numbers
            text = re.sub(r'[^\d\-]', '', text)
        
        # Health/mana format corrections (e.g., "1OO/1OO" -> "100/100")
        health_pattern = re.compile(r'\d+[/]\d+')
        if health_pattern.search(text):
            text = text.replace('O', '0').replace('l', '1').replace('I', '1')
        
        return text
    
    def _apply_spell_corrections(self, text: str) -> str:
        """Apply spell checking and corrections"""
        words = text.split()
        corrected_words = []
        
        for word in words:
            # Skip if word is in gaming vocabulary
            if word.lower() in self.gaming_vocabulary:
                corrected_words.append(word)
                continue
            
            # Skip if word looks like a number or special gaming element
            if re.match(r'^\d+$', word) or re.match(r'^\d+/\d+$', word):
                corrected_words.append(word)
                continue
            
            # Apply common corrections
            if word in self.common_corrections:
                corrected_words.append(self.common_corrections[word])
                continue
            
            # Use difflib for fuzzy matching against gaming vocabulary
            close_matches = difflib.get_close_matches(
                word.lower(), 
                self.gaming_vocabulary, 
                n=1, 
                cutoff=0.8
            )
            
            if close_matches:
                corrected_words.append(close_matches[0])
            else:
                corrected_words.append(word)
        
        return ' '.join(corrected_words)
    
    def _apply_temporal_filtering(self, results: List[OCRResult], frame_timestamp: float) -> List[OCRResult]:
        """Apply temporal consistency filtering"""
        # Add current results to history
        self.text_history.append({
            'timestamp': frame_timestamp,
            'results': results
        })
        
        # Keep only recent history
        cutoff_time = frame_timestamp - self.temporal_window_size
        self.text_history = [
            entry for entry in self.text_history 
            if entry['timestamp'] > cutoff_time
        ]
        
        # Analyze temporal consistency
        filtered_results = []
        
        for result in results:
            # Check if this text appears consistently across frames
            consistency_score = self._calculate_temporal_consistency(result)
            
            # Only keep text that appears consistently or has high confidence
            if consistency_score > 0.3 or result.confidence > 0.8:
                filtered_results.append(result)
            else:
                self.correction_stats['temporal_corrections'] += 1
        
        return filtered_results
    
    def _calculate_temporal_consistency(self, result: OCRResult) -> float:
        """Calculate how consistently this text appears across recent frames"""
        if len(self.text_history) < 2:
            return 1.0
        
        text = result.text.lower().strip()
        bbox_center = self._get_bbox_center(result.bbox)
        
        # Count similar text in nearby locations across recent frames
        similar_count = 0
        total_frames = len(self.text_history)
        
        for history_entry in self.text_history:
            for hist_result in history_entry['results']:
                hist_text = hist_result.text.lower().strip()
                hist_center = self._get_bbox_center(hist_result.bbox)
                
                # Check text similarity
                text_similarity = difflib.SequenceMatcher(None, text, hist_text).ratio()
                
                # Check location similarity
                distance = np.sqrt(
                    (bbox_center[0] - hist_center[0])**2 + 
                    (bbox_center[1] - hist_center[1])**2
                )
                
                if text_similarity > 0.8 and distance < 50:
                    similar_count += 1
                    break
        
        return similar_count / total_frames
    
    def _get_bbox_center(self, bbox: List[List[int]]) -> Tuple[float, float]:
        """Get center point of bounding box"""
        x_coords = [point[0] for point in bbox]
        y_coords = [point[1] for point in bbox]
        return (sum(x_coords) / len(x_coords), sum(y_coords) / len(y_coords))
    
    def _regions_to_results(self, regions: List[TextRegion]) -> List[OCRResult]:
        """Convert text regions back to OCR results"""
        import time
        results = []

        for region in regions:
            result = OCRResult(
                text=region.text,
                confidence=region.confidence,
                bbox=region.bbox,
                timestamp=time.time()
            )
            results.append(result)

        return results
    
    def _load_gaming_vocabulary(self) -> Set[str]:
        """Load gaming-specific vocabulary"""
        vocabulary = {
            # Common gaming terms
            'health', 'mana', 'stamina', 'energy', 'hp', 'mp', 'sp', 'ep',
            'level', 'lvl', 'lv', 'experience', 'exp', 'xp',
            'damage', 'dmg', 'attack', 'defense', 'def', 'armor',
            'strength', 'str', 'agility', 'agi', 'intelligence', 'int',
            'dexterity', 'dex', 'constitution', 'con', 'wisdom', 'wis',
            'charisma', 'cha', 'luck', 'lck',
            
            # UI elements
            'inventory', 'skills', 'quests', 'map', 'settings', 'options',
            'save', 'load', 'exit', 'menu', 'pause', 'resume',
            'new', 'game', 'continue', 'start', 'begin',
            
            # Actions
            'attack', 'defend', 'cast', 'use', 'equip', 'unequip',
            'buy', 'sell', 'trade', 'craft', 'upgrade', 'enhance',
            'move', 'run', 'walk', 'jump', 'climb', 'swim',
            
            # Items
            'sword', 'shield', 'bow', 'staff', 'wand', 'dagger',
            'helmet', 'armor', 'boots', 'gloves', 'ring', 'amulet',
            'potion', 'scroll', 'key', 'gold', 'coin', 'gem',
            
            # Status effects
            'poison', 'burn', 'freeze', 'stun', 'sleep', 'charm',
            'blind', 'silence', 'slow', 'haste', 'berserk', 'rage'
        }
        
        return vocabulary
    
    def _load_common_corrections(self) -> Dict[str, str]:
        """Load common OCR error corrections"""
        return {
            # Common OCR mistakes
            'Heaith': 'Health',
            'Manna': 'Mana',
            'Experiance': 'Experience',
            'Inventary': 'Inventory',
            'Skilis': 'Skills',
            'Achievments': 'Achievements',
            'Equiped': 'Equipped',
            'Unequiped': 'Unequipped',
            'Atack': 'Attack',
            'Defens': 'Defense',
            'Magick': 'Magic',
            'Strenght': 'Strength',
            'Inteligence': 'Intelligence',
            'Dexterety': 'Dexterity',
            'Constitition': 'Constitution',
            'Charizma': 'Charisma',
            
            # Number/letter confusions in context
            'O0': '00',
            'l1': '11',
            'Il': '11',
            'S5': '55',
            'B8': '88'
        }
    
    def get_correction_stats(self) -> Dict:
        """Get correction statistics"""
        return self.correction_stats.copy()
    
    def reset_correction_stats(self):
        """Reset correction statistics"""
        self.correction_stats = {
            'total_corrections': 0,
            'spell_corrections': 0,
            'gaming_corrections': 0,
            'temporal_corrections': 0
        }
    
    def clear_temporal_history(self):
        """Clear temporal filtering history"""
        self.text_history = []
        self.stable_text_regions = {}
