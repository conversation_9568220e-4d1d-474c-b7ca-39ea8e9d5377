"""
Core OCR Pipeline Implementation using PaddleOCR

This module provides the main OCR pipeline class with PaddleOCR integration,
supporting both PP-OCRv5 and PGNet models for real-time text recognition.
"""

import time
import logging
from typing import List, Dict, Tuple, Optional, Union
import numpy as np
import cv2
from paddleocr import PaddleOCR
from dataclasses import dataclass
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ModelType(Enum):
    """Supported OCR model types"""
    PP_OCR_V5 = "pp_ocrv5"
    PGNET = "pgnet"


@dataclass
class OCRResult:
    """Data class for OCR detection results"""
    text: str
    confidence: float
    bbox: List[List[int]]  # Bounding box coordinates
    timestamp: float


@dataclass
class OCRConfig:
    """Configuration for OCR pipeline"""
    model_type: ModelType = ModelType.PP_OCR_V5
    use_gpu: bool = True
    use_angle_cls: bool = True
    use_space_char: bool = True
    lang: str = 'en'  # Language: 'en', 'ch', 'japan', 'korean', etc.
    confidence_threshold: float = 0.5
    det_db_thresh: float = 0.3
    det_db_box_thresh: float = 0.6
    det_db_unclip_ratio: float = 1.5
    max_text_length: int = 25
    rec_batch_num: int = 6
    drop_score: float = 0.5


class OCRPipeline:
    """
    High-performance OCR pipeline using PaddleOCR for real-time text recognition
    """
    
    def __init__(self, config: OCRConfig = None):
        """
        Initialize the OCR pipeline
        
        Args:
            config: OCR configuration object
        """
        self.config = config or OCRConfig()
        self.ocr_engine = None
        self.is_initialized = False
        self.performance_stats = {
            'total_frames': 0,
            'total_time': 0.0,
            'avg_fps': 0.0,
            'last_inference_time': 0.0
        }
        
        self._initialize_ocr()
    
    def _initialize_ocr(self):
        """Initialize the PaddleOCR engine with specified configuration"""
        try:
            logger.info(f"Initializing PaddleOCR with {self.config.model_type.value} model...")
            
            # Configure PaddleOCR parameters
            ocr_params = {
                'use_angle_cls': self.config.use_angle_cls,
                'lang': self.config.lang,
                'use_gpu': self.config.use_gpu,
                'use_space_char': self.config.use_space_char
            }
            
            # Initialize based on model type
            if self.config.model_type == ModelType.PGNET:
                # PGNet for end-to-end recognition (faster but potentially less accurate)
                ocr_params['use_pdserving'] = False
                ocr_params['det'] = False  # PGNet handles detection and recognition together
            
            self.ocr_engine = PaddleOCR(**ocr_params)
            self.is_initialized = True
            
            logger.info("PaddleOCR initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize PaddleOCR: {e}")
            raise RuntimeError(f"OCR initialization failed: {e}")
    
    def recognize_text(self, image: np.ndarray) -> List[OCRResult]:
        """
        Perform text recognition on an image
        
        Args:
            image: Input image as numpy array (BGR format)
            
        Returns:
            List of OCRResult objects containing detected text and metadata
        """
        if not self.is_initialized:
            raise RuntimeError("OCR pipeline not initialized")
        
        start_time = time.time()
        
        try:
            # Perform OCR inference
            results = self.ocr_engine.ocr(image, cls=self.config.use_angle_cls)
            
            # Process results
            ocr_results = []
            if results and results[0]:  # Check if results exist
                for detection in results[0]:
                    if detection and len(detection) >= 2:
                        bbox = detection[0]
                        text_info = detection[1]
                        
                        if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                            text = text_info[0]
                            confidence = text_info[1]
                            
                            # Filter by confidence threshold
                            if confidence >= self.config.confidence_threshold:
                                ocr_results.append(OCRResult(
                                    text=text,
                                    confidence=confidence,
                                    bbox=bbox,
                                    timestamp=start_time
                                ))
            
            # Update performance statistics
            inference_time = time.time() - start_time
            self._update_performance_stats(inference_time)
            
            return ocr_results
            
        except Exception as e:
            logger.error(f"OCR recognition failed: {e}")
            return []
    
    def recognize_text_batch(self, images: List[np.ndarray]) -> List[List[OCRResult]]:
        """
        Perform batch text recognition on multiple images
        
        Args:
            images: List of input images as numpy arrays
            
        Returns:
            List of lists containing OCRResult objects for each image
        """
        results = []
        for image in images:
            results.append(self.recognize_text(image))
        return results
    
    def _update_performance_stats(self, inference_time: float):
        """Update performance statistics"""
        self.performance_stats['total_frames'] += 1
        self.performance_stats['total_time'] += inference_time
        self.performance_stats['last_inference_time'] = inference_time
        
        if self.performance_stats['total_time'] > 0:
            self.performance_stats['avg_fps'] = (
                self.performance_stats['total_frames'] / 
                self.performance_stats['total_time']
            )
    
    def get_performance_stats(self) -> Dict:
        """Get current performance statistics"""
        return self.performance_stats.copy()
    
    def reset_performance_stats(self):
        """Reset performance statistics"""
        self.performance_stats = {
            'total_frames': 0,
            'total_time': 0.0,
            'avg_fps': 0.0,
            'last_inference_time': 0.0
        }
    
    def update_config(self, new_config: OCRConfig):
        """
        Update OCR configuration and reinitialize if necessary
        
        Args:
            new_config: New configuration object
        """
        if (new_config.model_type != self.config.model_type or 
            new_config.use_gpu != self.config.use_gpu or
            new_config.lang != self.config.lang):
            # Reinitialize if critical parameters changed
            self.config = new_config
            self._initialize_ocr()
        else:
            # Update configuration without reinitializing
            self.config = new_config
    
    def __del__(self):
        """Cleanup resources"""
        if hasattr(self, 'ocr_engine') and self.ocr_engine:
            del self.ocr_engine
